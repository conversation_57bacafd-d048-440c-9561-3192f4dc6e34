<!DOCTYPE html>
<html lang="th">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="บริการขนส่งโลจิสติกส์ครบวงจร ขนส่งตู้คอนเทนเนอร์ คลังสินค้า นำเข้าและส่งออก | NK Solutions & Logistics Co.,LTD" />
  <meta name="keywords" content="โลจิสติกส์, ขนส่ง, คอนเทนเนอร์, คลังสินค้า, นำเข้า, ส่งออก, logistics, shipping, container, warehouse" />
  <title>NK Solutions & Logistics Co.,LTD - บริการขนส่งโลจิสติกส์ครบวงจร ขนส่งตู้คอนเทนเนอร์</title>

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="NK Solutions & Logistics Co.,LTD บริการขนส่งโลจิสติกส์ครบวงจร ขนส่งตู้คอนเทนเนอร์" />
  <meta property="og:description" content="บริการขนส่งโลจิสติกส์ครบวงจร ขนส่งตู้คอนเทนเนอร์ คลังสินค้า นำเข้าและส่งออก | NK Solutions & Logistics Co.,LTD" />
  <meta property="og:image" content="assets/logo.png" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://nkslgroup.com/" />

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="NK Solutions & Logistics Co.,LTD" />
  <meta name="twitter:description" content="บริการขนส่งโลจิสติกส์ครบวงจร ขนส่งตู้คอนเทนเนอร์ คลังสินค้า นำเข้าและส่งออก" />

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/favicon-32.png">
  <link rel="apple-touch-icon-precomposed" sizes="180x180" href="assets/favicon-180.png">
  <link rel="apple-touch-icon-precomposed" sizes="167x167" href="assets/favicon-167.png">
  <link rel="apple-touch-icon-precomposed" sizes="152x152" href="assets/favicon-152.png">
  <link rel="apple-touch-icon-precomposed" sizes="120x120" href="assets/favicon-120.png">
  <link rel="apple-touch-icon-precomposed" sizes="114x114" href="assets/favicon-114.png">
  <link rel="apple-touch-icon-precomposed" sizes="72x72" href="assets/favicon-72.png">
  <link rel="apple-touch-icon-precomposed" href="assets/favicon-57.png">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- AOS Animation Library -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

  <!-- Styles -->
  <link rel="stylesheet" href="assets/style.css" />
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen">
    <div class="loading-spinner"></div>
    <p>กำลังโหลด...</p>
  </div>

  <!-- Header -->
  <header class="header">
    <div class="container">
      <div class="header-content">
        <div class="logo">
          <img src="assets/logoNK.png" alt="NK Solutions & Logistics" />
          <span class="logo-text">NK Solutions & Logistics</span>
        </div>

        <!-- Mobile Menu Button -->
        <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="เปิดเมนู">
          <span></span>
          <span></span>
          <span></span>
        </button>

        <!-- Navigation -->
        <nav class="nav" id="mainNav">
          <ul class="nav-links">
            <li><a href="#hero" id="nav-home">หน้าแรก</a></li>
            <li><a href="#services" id="nav-services">บริการ</a></li>
            <li><a href="#about" id="nav-about">เกี่ยวกับเรา</a></li>
            <li><a href="#clients" id="nav-clients">ลูกค้า</a></li>
            <li><a href="#contact" id="nav-contact">ติดต่อ</a></li>
          </ul>

          <div class="nav-controls">
            <button id="themeToggle" class="theme-toggle" aria-label="เปลี่ยนธีม">
              <span class="theme-icon">🌓</span>
            </button>
            <button id="langToggle" class="lang-toggle">
              <span id="langText">EN</span>
            </button>
          </div>
        </nav>
      </div>
    </div>
  </header>
  <!-- Hero Section -->
  <section id="hero" class="hero">
    <div class="hero-background">
      <div class="hero-particles"></div>
    </div>

    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 id="hero-title" class="hero-title"></h1>
          <p id="hero-subtitle" class="hero-subtitle"></p>

          <div class="hero-stats">
            <div class="stat-item">
              <span class="stat-number" data-count="500">0</span>
              <span class="stat-label" id="stat-clients">ลูกค้า</span>
            </div>
            <div class="stat-item">
              <span class="stat-number" data-count="10">0</span>
              <span class="stat-label" id="stat-years">ปีประสบการณ์</span>
            </div>
            <div class="stat-item">
              <span class="stat-number" data-count="1000">0</span>
              <span class="stat-label" id="stat-shipments">การขนส่ง/เดือน</span>
            </div>
          </div>

          <div class="hero-buttons">
            <button class="cta primary" id="hero-cta"></button>
            <button class="cta secondary" id="hero-learn-more">เรียนรู้เพิ่มเติม</button>
          </div>
        </div>

        <div class="hero-visual">
          <div id="truck-path">
            <div class="logistics-scene">
              <!-- Background Landscape -->
              <div class="landscape-background">
                <div class="sky"></div>
                <div class="mountains"></div>
                <div class="buildings"></div>
                <div class="road"></div>
              </div>

              <!-- Landmarks along the route -->
              <div class="landmark warehouse-start">
                <div class="building-icon">🏭</div>
                <span class="landmark-label">คลังสินค้า</span>
              </div>

              <div class="landmark city-checkpoint">
                <div class="building-icon">🏢</div>
                <span class="landmark-label">เมือง</span>
              </div>

              <div class="landmark port-destination">
                <div class="building-icon">🚢</div>
                <span class="landmark-label">ท่าเรือ</span>
              </div>

              <svg viewBox="0 0 800 400" preserveAspectRatio="xMidYMid meet" class="route-svg">
                <defs>
                  <linearGradient id="routeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#007bff;stop-opacity:0.3" />
                    <stop offset="50%" style="stop-color:#007bff;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#28a745;stop-opacity:0.8" />
                  </linearGradient>

                  <!-- Cargo Box Pattern -->
                  <g id="cargoBox">
                    <rect x="-3" y="-3" width="6" height="6" fill="#8B4513" stroke="#654321" stroke-width="0.5" rx="1"/>
                    <text x="0" y="1" text-anchor="middle" font-size="3" fill="#FFF">📦</text>
                  </g>
                </defs>

                <!-- Road Path -->
                <path id="route" d="M100,200 C300,50 500,350 700,200"
                      fill="none"
                      stroke="url(#routeGradient)"
                      stroke-width="6"
                      stroke-dasharray="15,8"
                      opacity="0.8" />

                <!-- Loading Animation at Start -->
                <g id="loadingCargo" class="cargo-loading">
                  <use href="#cargoBox" x="85" y="185">
                    <animateTransform attributeName="transform"
                                    type="translate"
                                    values="0,20; 0,0; 0,0; 0,0"
                                    dur="12s"
                                    repeatCount="indefinite"/>
                    <animate attributeName="opacity"
                           values="1; 1; 0; 0"
                           dur="12s"
                           repeatCount="indefinite"/>
                  </use>
                  <use href="#cargoBox" x="95" y="185">
                    <animateTransform attributeName="transform"
                                    type="translate"
                                    values="0,20; 0,0; 0,0; 0,0"
                                    dur="12s"
                                    begin="0.5s"
                                    repeatCount="indefinite"/>
                    <animate attributeName="opacity"
                           values="1; 1; 0; 0"
                           dur="12s"
                           begin="0.5s"
                           repeatCount="indefinite"/>
                  </use>
                  <use href="#cargoBox" x="105" y="185">
                    <animateTransform attributeName="transform"
                                    type="translate"
                                    values="0,20; 0,0; 0,0; 0,0"
                                    dur="12s"
                                    begin="1s"
                                    repeatCount="indefinite"/>
                    <animate attributeName="opacity"
                           values="1; 1; 0; 0"
                           dur="12s"
                           begin="1s"
                           repeatCount="indefinite"/>
                  </use>
                </g>

                <!-- Truck with Cargo -->
                <g id="truck">
                  <!-- Truck Body -->
                  <rect x="-30" y="-12" width="55" height="24" rx="6" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
                  <!-- Truck Cabin -->
                  <rect x="-25" y="-10" width="18" height="20" rx="3" fill="#ffffff" stroke="#007bff" stroke-width="1"/>
                  <!-- Cargo Area -->
                  <rect x="-5" y="-10" width="28" height="20" rx="2" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                  <!-- Cargo Boxes in Truck -->
                  <g id="truckCargo">
                    <use href="#cargoBox" x="5" y="-5">
                      <animate attributeName="opacity"
                             values="0; 1; 1; 0"
                             dur="12s"
                             begin="2s"
                             repeatCount="indefinite"/>
                    </use>
                    <use href="#cargoBox" x="12" y="-5">
                      <animate attributeName="opacity"
                             values="0; 1; 1; 0"
                             dur="12s"
                             begin="2.5s"
                             repeatCount="indefinite"/>
                    </use>
                    <use href="#cargoBox" x="19" y="-5">
                      <animate attributeName="opacity"
                             values="0; 1; 1; 0"
                             dur="12s"
                             begin="3s"
                             repeatCount="indefinite"/>
                    </use>
                  </g>

                  <!-- Company Logo -->
                  <text x="-18" y="5" font-size="12" fill="#007bff" font-family="Kanit, sans-serif" font-weight="bold">NK</text>

                  <!-- Wheels -->
                  <circle cx="-18" cy="14" r="7" fill="#333" stroke="#000" stroke-width="1"/>
                  <circle cx="18" cy="14" r="7" fill="#333" stroke="#000" stroke-width="1"/>
                  <circle cx="-18" cy="14" r="4" fill="#666"/>
                  <circle cx="18" cy="14" r="4" fill="#666"/>

                  <!-- Truck Animation -->
                  <animateMotion dur="12s" repeatCount="indefinite" rotate="auto">
                    <mpath href="#route" />
                  </animateMotion>
                </g>

                <!-- Unloading Animation at End -->
                <g id="unloadingCargo" class="cargo-unloading">
                  <use href="#cargoBox" x="715" y="185">
                    <animateTransform attributeName="transform"
                                    type="translate"
                                    values="0,0; 0,0; 0,20; 0,20"
                                    dur="12s"
                                    begin="9s"
                                    repeatCount="indefinite"/>
                    <animate attributeName="opacity"
                           values="0; 1; 1; 1"
                           dur="12s"
                           begin="9s"
                           repeatCount="indefinite"/>
                  </use>
                  <use href="#cargoBox" x="725" y="185">
                    <animateTransform attributeName="transform"
                                    type="translate"
                                    values="0,0; 0,0; 0,20; 0,20"
                                    dur="12s"
                                    begin="9.5s"
                                    repeatCount="indefinite"/>
                    <animate attributeName="opacity"
                           values="0; 1; 1; 1"
                           dur="12s"
                           begin="9.5s"
                           repeatCount="indefinite"/>
                  </use>
                  <use href="#cargoBox" x="735" y="185">
                    <animateTransform attributeName="transform"
                                    type="translate"
                                    values="0,0; 0,0; 0,20; 0,20"
                                    dur="12s"
                                    begin="10s"
                                    repeatCount="indefinite"/>
                    <animate attributeName="opacity"
                           values="0; 1; 1; 1"
                           dur="12s"
                           begin="10s"
                           repeatCount="indefinite"/>
                  </use>
                </g>

                <!-- Route Points with Enhanced Animation -->
                <g class="route-point start-point">
                  <circle cx="100" cy="200" r="12" fill="#007bff" opacity="0.3"/>
                  <circle cx="100" cy="200" r="8" fill="#007bff">
                    <animate attributeName="r" values="8;15;8" dur="3s" repeatCount="indefinite" />
                    <animate attributeName="opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite" />
                  </circle>
                  <text x="100" y="235" text-anchor="middle" font-size="12" fill="#007bff" font-weight="bold">เริ่มต้น</text>
                </g>

                <g class="route-point end-point">
                  <circle cx="700" cy="200" r="12" fill="#28a745" opacity="0.3"/>
                  <circle cx="700" cy="200" r="8" fill="#28a745">
                    <animate attributeName="r" values="8;15;8" dur="3s" repeatCount="indefinite" begin="1.5s" />
                    <animate attributeName="opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite" begin="1.5s" />
                  </circle>
                  <text x="700" y="235" text-anchor="middle" font-size="12" fill="#28a745" font-weight="bold">ปลายทาง</text>
                </g>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator">
      <div class="scroll-arrow"></div>
    </div>
  </section>
  <!-- Services Section -->
  <section id="services" class="services">
    <div class="container">
      <div class="section-header">
        <h2 id="services-title" class="section-title">บริการของเรา</h2>
        <p id="services-subtitle" class="section-subtitle">บริการขนส่งและโลจิสติกส์ครบวงจรที่ตอบสนองทุกความต้องการของธุรกิจ</p>
      </div>

      <div class="services-grid">
        <div class="service-card" data-aos="fade-up" data-aos-delay="100">
          <div class="service-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2"></path>
              <path d="M15 18H9"></path>
              <path d="M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14"></path>
              <circle cx="17" cy="18" r="2"></circle>
              <circle cx="7" cy="18" r="2"></circle>
            </svg>
          </div>
          <h3 id="service-1-title">บริการขนส่งภายในประเทศ</h3>
          <p id="service-1-desc">ขนส่งสินค้าทั่วประเทศไทยด้วยระบบติดตามแบบเรียลไทม์ รวดเร็ว ปลอดภัย</p>
          <ul class="service-features">
            <li>ขนส่งตู้คอนเทนเนอร์</li>
            <li>ขนส่งสินค้าทั่วไป</li>
            <li>ขนส่งสินค้าพิเศษ</li>
          </ul>
        </div>

        <div class="service-card" data-aos="fade-up" data-aos-delay="200">
          <div class="service-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
              <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
              <line x1="12" y1="22.08" x2="12" y2="12"></line>
            </svg>
          </div>
          <h3 id="service-2-title">บริการคลังสินค้า</h3>
          <p id="service-2-desc">คลังสินค้าที่ทันสมัย ระบบจัดการสต็อกอัตโนมัติ พร้อมระบบรักษาความปลอดภัย</p>
          <ul class="service-features">
            <li>เก็บรักษาสินค้า</li>
            <li>จัดการสต็อก</li>
            <li>บรรจุภัณฑ์</li>
          </ul>
        </div>

        <div class="service-card" data-aos="fade-up" data-aos-delay="300">
          <div class="service-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M8 12l2 2 4-4"></path>
              <path d="M16 8l-6 6"></path>
              <path d="M8 8l8 8"></path>
            </svg>
          </div>
          <h3 id="service-3-title">นำเข้า - ส่งออก</h3>
          <p id="service-3-desc">บริการด้านการค้าระหว่างประเทศ จัดการเอกสาร ผ่านพิธีการศุลกากร</p>
          <ul class="service-features">
            <li>จัดการเอกสารนำเข้า-ส่งออก</li>
            <li>ผ่านพิธีการศุลกากร</li>
            <li>ประสานงานกับหน่วยงานต่างๆ</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about">
    <div class="container">
      <div class="about-content">
        <div class="about-text" data-aos="fade-right">
          <h2 id="about-title" class="section-title">เกี่ยวกับ NK Solutions & Logistics</h2>
          <p id="about-desc" class="about-description">
            เราเป็นบริษัทผู้เชี่ยวชาญด้านการขนส่งและโลจิสติกส์ที่มีประสบการณ์กว่า 10 ปี
            ในการให้บริการลูกค้าทั้งในและต่างประเทศ ด้วยทีมงานมืออาชีพและเทคโนโลยีที่ทันสมัย
          </p>

          <div class="about-features">
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span id="feature-1">ประสบการณ์กว่า 10 ปี</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span id="feature-2">ทีมงานมืออาชีพ</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span id="feature-3">เทคโนโลยีทันสมัย</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">✓</div>
              <span id="feature-4">บริการ 24/7</span>
            </div>
          </div>
        </div>

        <div class="about-visual" data-aos="fade-left">
          <div class="about-image">
            <div class="image-placeholder">
              <svg viewBox="0 0 400 300" fill="none">
                <rect width="400" height="300" fill="#f8f9fa"/>
                <path d="M100 150L200 100L300 150L200 200Z" fill="#007bff" opacity="0.1"/>
                <circle cx="200" cy="150" r="50" fill="#007bff" opacity="0.2"/>
                <text x="200" y="155" text-anchor="middle" fill="#007bff" font-size="14">NK Logistics</text>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Clients Section -->
  <section id="clients" class="clients">
    <div class="container">
      <div class="section-header">
        <h2 id="clients-title" class="section-title">ลูกค้าที่ไว้วางใจ</h2>
        <p id="clients-subtitle" class="section-subtitle">บริษัทชั้นนำที่เลือกใช้บริการของเรา</p>
      </div>

      <div class="clients-grid">
        <div class="client-item" data-aos="zoom-in" data-aos-delay="100">
          <div class="client-logo">
            <img src="assets/client1.png" alt="3J" loading="lazy" />
          </div>
        </div>
        <div class="client-item" data-aos="zoom-in" data-aos-delay="150">
          <div class="client-logo">
            <img src="assets/client2.png" alt="Aeroklas" loading="lazy" />
          </div>
        </div>
        <div class="client-item" data-aos="zoom-in" data-aos-delay="200">
          <div class="client-logo">
            <img src="assets/client3.png" alt="AJJ Logistics" loading="lazy" />
          </div>
        </div>
        <div class="client-item" data-aos="zoom-in" data-aos-delay="250">
          <div class="client-logo">
            <img src="assets/client4.png" alt="America Metal" loading="lazy" />
          </div>
        </div>
        <div class="client-item" data-aos="zoom-in" data-aos-delay="300">
          <div class="client-logo">
            <img src="assets/client5.png" alt="ANJI" loading="lazy" />
          </div>
        </div>
        <div class="client-item" data-aos="zoom-in" data-aos-delay="350">
          <div class="client-logo">
            <img src="assets/client6.png" alt="BGF" loading="lazy" />
          </div>
        </div>
        <div class="client-item" data-aos="zoom-in" data-aos-delay="400">
          <div class="client-logo">
            <img src="assets/client7.png" alt="BGC" loading="lazy" />
          </div>
        </div>
      </div>

      <!-- Testimonials -->
      <div class="testimonials" data-aos="fade-up">
        <div class="testimonial-slider">
          <div class="testimonial-item active">
            <div class="testimonial-content">
              <p id="testimonial-1">"บริการขนส่งที่เชื่อถือได้ ทีมงานมืออาชีพ ส่งของตรงเวลาเสมอ"</p>
              <div class="testimonial-author">
                <strong>คุณสมชาย ใจดี</strong>
                <span>ผู้จัดการฝ่ายโลจิสติกส์ - บริษัท ABC จำกัด</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="contact-content">
        <div class="contact-info" data-aos="fade-right">
          <h2 id="form-title" class="section-title"></h2>
          <p id="contact-desc" class="contact-description">
            พร้อมให้บริการและตอบคำถามทุกข้อสงสัย ติดต่อเราได้ทุกช่องทาง
          </p>

          <div class="contact-details">
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </div>
              <div class="contact-text">
                <h4>โทรศัพท์</h4>
                <p>+66 2-xxx-xxxx</p>
              </div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </div>
              <div class="contact-text">
                <h4>อีเมล</h4>
                <p><EMAIL></p>
              </div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
              </div>
              <div class="contact-text">
                <h4>ที่อยู่</h4>
                <p>739 ซอยพุทธมณฑลสาย 1 แยก 21 แขวงบางระมาด เขตตลิ่งชัน กทม. 10170</p>
              </div>
            </div>
          </div>
        </div>

        <div class="contact-form-wrapper" data-aos="fade-left">
          <form id="contactForm" class="contact-form" method="POST" action="send_mail.php">
            <div class="form-group">
              <input type="text" name="name" id="form-name" placeholder="" required />
              <label for="form-name" id="form-name-label">ชื่อของคุณ</label>
            </div>

            <div class="form-group">
              <input type="email" name="email" id="form-email" placeholder="" required />
              <label for="form-email" id="form-email-label">อีเมล</label>
            </div>

            <div class="form-group">
              <input type="tel" name="phone" id="form-phone" placeholder="" />
              <label for="form-phone" id="form-phone-label">เบอร์โทรศัพท์</label>
            </div>

            <div class="form-group">
              <textarea name="message" id="form-message" placeholder="" required rows="5"></textarea>
              <label for="form-message" id="form-message-label">ข้อความ</label>
            </div>

            <button type="submit" id="form-submit" class="submit-btn">
              <span class="btn-text"></span>
              <span class="btn-loading">กำลังส่ง...</span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-logo">
            <img src="assets/logoNK.png" alt="NK Solutions & Logistics" />
            <span>NK Solutions & Logistics</span>
          </div>
          <p class="footer-desc">
            ผู้เชี่ยวชาญด้านการขนส่งและโลจิสติกส์ที่คุณไว้วางใจได้
          </p>
        </div>

        <div class="footer-section">
          <h4>บริการ</h4>
          <ul>
            <li><a href="#services">ขนส่งภายในประเทศ</a></li>
            <li><a href="#services">คลังสินค้า</a></li>
            <li><a href="#services">นำเข้า-ส่งออก</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>ติดต่อเรา</h4>
          <ul>
            <li>โทร: +66 2-xxx-xxxx</li>
            <li>อีเมล: <EMAIL></li>
            <li>เวลาทำการ: จ-ศ 8:00-17:00</li>
          </ul>
        </div>

        <div class="footer-section">
          <h4>ติดตามเรา</h4>
          <div class="social-links">
            <a href="#" aria-label="Facebook">📘</a>
            <a href="#" aria-label="Line">💬</a>
            <a href="#" aria-label="Email">📧</a>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <p id="footer-copy"></p>
      </div>
    </div>
  </footer>

  <!-- Back to Top Button -->
  <button id="backToTop" class="back-to-top" aria-label="กลับไปด้านบน">
    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M18 15l-6-6-6 6"></path>
    </svg>
  </button>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="js/app.js"></script>
</body>
</html>