// Enhanced NK Logistics Website JavaScript
document.addEventListener("DOMContentLoaded", async () => {
  // Initialize AOS (Animate On Scroll)
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100
    });
  }

  // DOM Elements
  const themeToggle = document.getElementById("themeToggle");
  const langToggle = document.getElementById("langToggle");
  const mobileMenuBtn = document.getElementById("mobileMenuBtn");
  const mainNav = document.getElementById("mainNav");
  const backToTop = document.getElementById("backToTop");
  const contactForm = document.getElementById("contactForm");
  const loadingScreen = document.getElementById("loading-screen");

  // State
  let currentLang = localStorage.getItem("lang") || "th";
  let currentTheme = localStorage.getItem("theme") || "light";

  // Initialize theme
  document.body.classList.toggle("dark", currentTheme === "dark");

  // Load language data
  let langData;
  try {
    langData = await fetch("lang.json").then(res => res.json());
    updateLang(currentLang);
  } catch (error) {
    console.error("Failed to load language data:", error);
  }

  // Hide loading screen
  setTimeout(() => {
    loadingScreen.classList.add('hidden');
  }, 1000);

  // Theme Toggle
  themeToggle.addEventListener("click", () => {
    currentTheme = currentTheme === "light" ? "dark" : "light";
    document.body.classList.toggle("dark", currentTheme === "dark");
    localStorage.setItem("theme", currentTheme);

    // Update theme icon
    const themeIcon = themeToggle.querySelector('.theme-icon');
    themeIcon.textContent = currentTheme === "dark" ? "☀️" : "🌙";
  });

  // Language Toggle
  langToggle.addEventListener("click", () => {
    currentLang = currentLang === "th" ? "en" : "th";
    localStorage.setItem("lang", currentLang);
    updateLang(currentLang);

    // Update language button text
    document.getElementById("langText").textContent = currentLang === "th" ? "EN" : "TH";
  });

  // Mobile Menu Toggle
  mobileMenuBtn.addEventListener("click", () => {
    mobileMenuBtn.classList.toggle("active");
    mainNav.classList.toggle("active");
  });

  // Close mobile menu when clicking on nav links
  document.querySelectorAll('.nav-links a').forEach(link => {
    link.addEventListener('click', () => {
      mobileMenuBtn.classList.remove("active");
      mainNav.classList.remove("active");
    });
  });

  // Smooth scrolling for navigation links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const targetPosition = target.offsetTop - headerHeight;

        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });

  // Back to Top Button
  window.addEventListener('scroll', () => {
    if (window.pageYOffset > 300) {
      backToTop.classList.add('visible');
    } else {
      backToTop.classList.remove('visible');
    }
  });

  backToTop.addEventListener('click', () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  // Header scroll effect
  let lastScrollTop = 0;
  const header = document.querySelector('.header');

  window.addEventListener('scroll', () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (scrollTop > lastScrollTop && scrollTop > 100) {
      // Scrolling down
      header.style.transform = 'translateY(-100%)';
    } else {
      // Scrolling up
      header.style.transform = 'translateY(0)';
    }

    lastScrollTop = scrollTop;
  });

  // Animated counters for statistics
  function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-count'));
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;

      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        counter.textContent = Math.floor(current).toLocaleString();
      }, 16);
    });
  }

  // Trigger counter animation when hero section is visible
  const heroSection = document.getElementById('hero');
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateCounters();
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });

  if (heroSection) {
    observer.observe(heroSection);
  }

  // Contact Form Enhancement
  if (contactForm) {
    contactForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const submitBtn = contactForm.querySelector('#form-submit');
      const btnText = submitBtn.querySelector('.btn-text');
      const btnLoading = submitBtn.querySelector('.btn-loading');

      // Show loading state
      submitBtn.classList.add('loading');
      submitBtn.disabled = true;

      try {
        const formData = new FormData(contactForm);

        // Simulate form submission (replace with actual endpoint)
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Show success message
        showNotification('ส่งข้อความเรียบร้อยแล้ว ขอบคุณที่ติดต่อเรา!', 'success');
        contactForm.reset();

      } catch (error) {
        console.error('Form submission error:', error);
        showNotification('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', 'error');
      } finally {
        // Reset button state
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
      }
    });
  }

  // Notification system
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add styles
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '1rem 1.5rem',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '500',
      zIndex: '10000',
      transform: 'translateX(100%)',
      transition: 'transform 0.3s ease',
      backgroundColor: type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'
    });

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 5000);
  }

  // Update language function
  function updateLang(lang) {
    if (!langData || !langData[lang]) return;

    const t = langData[lang];

    // Update content
    const elements = {
      'hero-title': t.hero?.title,
      'hero-subtitle': t.hero?.subtitle,
      'hero-cta': t.hero?.cta,
      'hero-learn-more': lang === 'th' ? 'เรียนรู้เพิ่มเติม' : 'Learn More',
      'form-title': t.form?.title,
      'footer-copy': t.footer?.copyright,

      // Navigation
      'nav-home': lang === 'th' ? 'หน้าแรก' : 'Home',
      'nav-services': lang === 'th' ? 'บริการ' : 'Services',
      'nav-about': lang === 'th' ? 'เกี่ยวกับเรา' : 'About',
      'nav-clients': lang === 'th' ? 'ลูกค้า' : 'Clients',
      'nav-contact': lang === 'th' ? 'ติดต่อ' : 'Contact',

      // Services
      'services-title': lang === 'th' ? 'บริการของเรา' : 'Our Services',
      'services-subtitle': lang === 'th' ? 'บริการขนส่งและโลจิสติกส์ครบวงจรที่ตอบสนองทุกความต้องการของธุรกิจ' : 'Complete logistics and transportation services that meet all your business needs',

      // About
      'about-title': lang === 'th' ? 'เกี่ยวกับ NK Solutions & Logistics' : 'About NK Solutions & Logistics',
      'about-desc': lang === 'th' ? 'เราเป็นบริษัทผู้เชี่ยวชาญด้านการขนส่งและโลจิสติกส์ที่มีประสบการณ์กว่า 10 ปี ในการให้บริการลูกค้าทั้งในและต่างประเทศ ด้วยทีมงานมืออาชีพและเทคโนโลยีที่ทันสมัย' : 'We are a logistics and transportation company with over 10 years of experience serving domestic and international clients with professional teams and modern technology',

      // Clients
      'clients-title': lang === 'th' ? 'ลูกค้าที่ไว้วางใจ' : 'Trusted Clients',
      'clients-subtitle': lang === 'th' ? 'บริษัทชั้นนำที่เลือกใช้บริการของเรา' : 'Leading companies that choose our services',

      // Contact
      'contact-desc': lang === 'th' ? 'พร้อมให้บริการและตอบคำถามทุกข้อสงสัย ติดต่อเราได้ทุกช่องทาง' : 'Ready to serve and answer all your questions. Contact us through any channel',

      // Stats
      'stat-clients': lang === 'th' ? 'ลูกค้า' : 'Clients',
      'stat-years': lang === 'th' ? 'ปีประสบการณ์' : 'Years Experience',
      'stat-shipments': lang === 'th' ? 'การขนส่ง/เดือน' : 'Shipments/Month'
    };

    // Update text content
    Object.entries(elements).forEach(([id, text]) => {
      const element = document.getElementById(id);
      if (element && text) {
        element.textContent = text;
      }
    });

    // Update form placeholders and labels
    const formElements = {
      'form-name': t.form?.name,
      'form-email': t.form?.email,
      'form-phone': lang === 'th' ? 'เบอร์โทรศัพท์' : 'Phone Number',
      'form-message': t.form?.message
    };

    Object.entries(formElements).forEach(([id, placeholder]) => {
      const element = document.getElementById(id);
      if (element && placeholder) {
        element.placeholder = placeholder;
      }
    });

    // Update form labels
    const labelElements = {
      'form-name-label': t.form?.name,
      'form-email-label': t.form?.email,
      'form-phone-label': lang === 'th' ? 'เบอร์โทรศัพท์' : 'Phone Number',
      'form-message-label': t.form?.message
    };

    Object.entries(labelElements).forEach(([id, text]) => {
      const element = document.getElementById(id);
      if (element && text) {
        element.textContent = text;
      }
    });

    // Update submit button
    const submitBtn = document.querySelector('#form-submit .btn-text');
    if (submitBtn && t.form?.submit) {
      submitBtn.textContent = t.form.submit;
    }

    // Update HTML lang attribute
    document.documentElement.lang = lang;
  }

  // Initialize language button text
  document.getElementById("langText").textContent = currentLang === "th" ? "EN" : "TH";

  // Initialize theme icon
  const themeIcon = themeToggle.querySelector('.theme-icon');
  themeIcon.textContent = currentTheme === "dark" ? "☀️" : "🌙";
});