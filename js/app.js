document.addEventListener("DOMContentLoaded", async () => {
  const themeToggle = document.getElementById("themeToggle");
  const langToggle = document.getElementById("langToggle");

  let currentLang = localStorage.getItem("lang") || "th";
  let currentTheme = localStorage.getItem("theme") || "light";

  document.body.classList.toggle("dark", currentTheme === "dark");

  const langData = await fetch("lang.json").then(res => res.json());
  updateLang(currentLang);

  langToggle.addEventListener("click", () => {
    currentLang = currentLang === "th" ? "en" : "th";
    localStorage.setItem("lang", currentLang);
    updateLang(currentLang);
  });

  themeToggle.addEventListener("click", () => {
    currentTheme = currentTheme === "light" ? "dark" : "light";
    document.body.classList.toggle("dark", currentTheme === "dark");
    localStorage.setItem("theme", currentTheme);
  });

  function updateLang(lang) {
    const t = langData[lang];
    document.getElementById("hero-title").textContent = t.hero.title;
    document.getElementById("hero-subtitle").textContent = t.hero.subtitle;
    document.getElementById("hero-cta").textContent = t.hero.cta;
    document.getElementById("form-title").textContent = t.form.title;
    document.getElementById("form-name").placeholder = t.form.name;
    document.getElementById("form-email").placeholder = t.form.email;
    document.getElementById("form-message").placeholder = t.form.message;
    document.getElementById("form-submit").textContent = t.form.submit;
    document.getElementById("footer-copy").textContent = t.footer.copyright;
  }
});